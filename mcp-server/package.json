{"name": "orders-portal-mcp-server", "version": "0.1.0", "type": "module", "bin": {"orders-portal-mcp-server": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "dev": "tsx watch src/index.ts", "test": "npm run build && npm run start", "test:auth": "npm run build && node test/auth-cookies.js", "test:tools": "npm run build && node test/all-tools.js", "test:quick": "npm run build && node test/quick-test.js", "inspect": "npx @modelcontextprotocol/inspector tsx src/index.ts", "inspect:build": "npm run build && npx @modelcontextprotocol/inspector node dist/index.js", "inspect:http": "node update-inspector-config.cjs && npx @modelcontextprotocol/inspector --config inspector.json --server orders-portal", "inspect:cli": "node update-inspector-config.cjs && npx @modelcontextprotocol/inspector --cli --config inspector.json --server orders-portal", "inspect:update": "node update-inspector-config.cjs", "oauth2:dev": "OAUTH2_ENABLED=true npm run dev", "oauth2:start": "OAUTH2_ENABLED=true npm run start", "keycloak:test": "node scripts/test-keycloak.js", "keycloak:configure": "node scripts/test-keycloak.js --apply", "http:dev": "TRANSPORT=http npm run dev", "http:start": "TRANSPORT=http npm run start", "http:oauth2": "TRANSPORT=http OAUTH2_ENABLED=true npm run dev", "dev:3000": "MCP_SERVER_PORT=3000 npm run dev", "dev:4000": "MCP_SERVER_PORT=4000 npm run dev", "dev:5000": "MCP_SERVER_PORT=5000 npm run dev", "dev:6200": "MCP_SERVER_PORT=6200 npm run dev", "dev:6300": "MCP_SERVER_PORT=6300 npm run dev", "dev:7000": "MCP_SERVER_PORT=7000 npm run dev", "dev:8000": "MCP_SERVER_PORT=8000 npm run dev", "dev:8080": "MCP_SERVER_PORT=8080 npm run dev", "dev:9000": "MCP_SERVER_PORT=9000 npm run dev", "port:status": "echo '🔍 Checking MCP Server Port Status:' && lsof -i :${MCP_SERVER_PORT:-6300} || echo 'Port is available'", "port:kill": "echo '🛑 Killing processes on MCP Server Port:' && lsof -ti :${MCP_SERVER_PORT:-6300} | xargs kill -9 || echo 'No processes found'", "inspect:3000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:3000/mcp\"", "inspect:4000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:4000/mcp\"", "inspect:5000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:5000/mcp\"", "inspect:6200": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:6200/mcp\"", "inspect:6300": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:6300/mcp\"", "inspect:7000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:7000/mcp\"", "inspect:8000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:8000/mcp\"", "inspect:8080": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:8080/mcp\"", "inspect:9000": "npx @modelcontextprotocol/inspector --cli --server \"http://localhost:9000/mcp\""}, "files": ["dist"], "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "orders-portal-mcp-server", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@types/node-jose": "^1.1.13", "axios": "^1.6.0", "dotenv": "^16.3.0", "eventsource": "^2.0.2", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "ws": "^8.18.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.1", "@types/express": "^5.0.1", "@types/node": "^20.0.0", "shx": "^0.3.4", "tsx": "^4.0.0", "typescript": "^5.0.0"}}