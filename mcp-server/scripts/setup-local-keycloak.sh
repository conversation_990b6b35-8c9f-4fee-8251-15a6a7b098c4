#!/bin/bash

# 🔐 Local Keycloak Setup Script
# This script sets up a local Keycloak instance for MCP server testing

set -e

echo "🚀 Local Keycloak Setup for MCP Server"
echo "======================================"

# Configuration
KEYCLOAK_VERSION="16.1.1"
KEYCLOAK_PORT="8080"
KEYCLOAK_ADMIN_USER="admin"
KEYCLOAK_ADMIN_PASS="admin123"
CONTAINER_NAME="mcp-keycloak"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is available
check_docker() {
    print_status "Checking Docker availability..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        echo "Please install Docker from: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        echo "Please start Docker and try again"
        exit 1
    fi
    
    print_success "Docker is available"
}

# Check if port is available
check_port() {
    print_status "Checking if port $KEYCLOAK_PORT is available..."
    
    if lsof -Pi :$KEYCLOAK_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Port $KEYCLOAK_PORT is already in use"
        
        # Check if it's our Keycloak container
        if docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -q "$CONTAINER_NAME.*$KEYCLOAK_PORT"; then
            print_success "Keycloak container is already running"
            return 0
        else
            print_error "Port $KEYCLOAK_PORT is used by another process"
            echo "Please stop the process using port $KEYCLOAK_PORT or choose a different port"
            exit 1
        fi
    fi
    
    print_success "Port $KEYCLOAK_PORT is available"
}

# Stop existing container
stop_existing() {
    print_status "Stopping existing Keycloak container..."
    
    if docker ps -a --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
        docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
        print_success "Removed existing container"
    else
        print_status "No existing container found"
    fi
}

# Start Keycloak container
start_keycloak() {
    print_status "Starting Keycloak container..."
    
    # Try different image sources
    IMAGES=(
        "jboss/keycloak:$KEYCLOAK_VERSION"
        "quay.io/keycloak/keycloak:legacy"
        "keycloak/keycloak:legacy"
    )
    
    for IMAGE in "${IMAGES[@]}"; do
        print_status "Trying image: $IMAGE"
        
        if docker run -d \
            --name $CONTAINER_NAME \
            -p $KEYCLOAK_PORT:8080 \
            -e KEYCLOAK_USER=$KEYCLOAK_ADMIN_USER \
            -e KEYCLOAK_PASSWORD=$KEYCLOAK_ADMIN_PASS \
            -e DB_VENDOR=H2 \
            $IMAGE >/dev/null 2>&1; then
            
            print_success "Started Keycloak with image: $IMAGE"
            return 0
        else
            print_warning "Failed to start with image: $IMAGE"
        fi
    done
    
    print_error "Failed to start Keycloak with any available image"
    return 1
}

# Wait for Keycloak to be ready
wait_for_keycloak() {
    print_status "Waiting for Keycloak to start (this may take 2-3 minutes)..."
    
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$KEYCLOAK_PORT/auth/realms/master" >/dev/null 2>&1; then
            print_success "Keycloak is ready!"
            return 0
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            print_status "Still waiting... (attempt $attempt/$max_attempts)"
        fi
        
        sleep 5
        ((attempt++))
    done
    
    print_error "Keycloak failed to start within expected time"
    print_status "Checking container logs..."
    docker logs $CONTAINER_NAME --tail 20
    return 1
}

# Test Keycloak endpoints
test_keycloak() {
    print_status "Testing Keycloak endpoints..."
    
    local base_url="http://localhost:$KEYCLOAK_PORT/auth"
    local realm_url="$base_url/realms/master"
    
    # Test realm endpoint
    if curl -s -f "$realm_url" >/dev/null; then
        print_success "Realm endpoint is accessible"
    else
        print_error "Realm endpoint is not accessible"
        return 1
    fi
    
    # Test OpenID configuration
    if curl -s -f "$realm_url/.well-known/openid-configuration" >/dev/null; then
        print_success "OpenID configuration is accessible"
    else
        print_error "OpenID configuration is not accessible"
        return 1
    fi
    
    print_success "All Keycloak endpoints are working"
}

# Generate MCP server configuration
generate_config() {
    print_status "Generating MCP server configuration..."
    
    local config_file=".env.keycloak.local"
    
    cat > "$config_file" << EOF
# 🔐 Local Keycloak Configuration for MCP Server
# Generated on: $(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")

# ================================
# OAuth2 Configuration
# ================================
OAUTH2_ENABLED=true

# ================================
# Local Keycloak Server Configuration
# ================================
KEYCLOAK_BASE_URL=http://localhost:$KEYCLOAK_PORT/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-server
OAUTH2_CLIENT_SECRET=

# ================================
# MCP Server Configuration
# ================================
MCP_SERVER_PORT=6300
TRANSPORT=http

# ================================
# Development Settings
# ================================
NODE_ENV=development
DEBUG_SERVICE_ADAPTER=true

# ================================
# Keycloak Admin Console
# ================================
# URL: http://localhost:$KEYCLOAK_PORT/auth/admin
# Username: $KEYCLOAK_ADMIN_USER
# Password: $KEYCLOAK_ADMIN_PASS

# ================================
# Next Steps
# ================================
# 1. Copy this config: cp $config_file .env
# 2. Configure Keycloak client (see KEYCLOAK_SETUP.md)
# 3. Start MCP server: npm run dev
# 4. Test OAuth2: npm run keycloak:test
EOF
    
    print_success "Configuration saved to: $config_file"
}

# Print final instructions
print_instructions() {
    echo ""
    echo "🎉 Local Keycloak Setup Complete!"
    echo "================================="
    echo ""
    echo "📋 Keycloak Information:"
    echo "   Admin Console: http://localhost:$KEYCLOAK_PORT/auth/admin"
    echo "   Username: $KEYCLOAK_ADMIN_USER"
    echo "   Password: $KEYCLOAK_ADMIN_PASS"
    echo "   Master Realm: http://localhost:$KEYCLOAK_PORT/auth/realms/master"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Configure OAuth2 client in Keycloak (see KEYCLOAK_SETUP.md)"
    echo "   2. Copy configuration: cp .env.keycloak.local .env"
    echo "   3. Start MCP server: npm run dev"
    echo "   4. Test setup: npm run keycloak:test"
    echo ""
    echo "📋 Useful Commands:"
    echo "   Stop Keycloak:    docker stop $CONTAINER_NAME"
    echo "   Start Keycloak:   docker start $CONTAINER_NAME"
    echo "   View logs:        docker logs -f $CONTAINER_NAME"
    echo "   Remove container: docker rm -f $CONTAINER_NAME"
    echo ""
}

# Main execution
main() {
    case "${1:-start}" in
        "start")
            check_docker
            check_port
            stop_existing
            if start_keycloak; then
                wait_for_keycloak
                test_keycloak
                generate_config
                print_instructions
            else
                print_error "Failed to start Keycloak"
                exit 1
            fi
            ;;
        "stop")
            print_status "Stopping Keycloak..."
            docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
            docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
            print_success "Keycloak stopped and removed"
            ;;
        "status")
            if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
                print_success "Keycloak is running"
                echo "   Admin Console: http://localhost:$KEYCLOAK_PORT/auth/admin"
                echo "   Container logs: docker logs -f $CONTAINER_NAME"
            else
                print_warning "Keycloak is not running"
            fi
            ;;
        "logs")
            docker logs -f $CONTAINER_NAME
            ;;
        *)
            echo "Usage: $0 {start|stop|status|logs}"
            echo ""
            echo "Commands:"
            echo "  start   - Start local Keycloak instance"
            echo "  stop    - Stop and remove Keycloak container"
            echo "  status  - Check if Keycloak is running"
            echo "  logs    - Show Keycloak container logs"
            exit 1
            ;;
    esac
}

main "$@"
