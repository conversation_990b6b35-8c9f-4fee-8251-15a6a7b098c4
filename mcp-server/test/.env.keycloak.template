# Keycloak Token Exchange Test Configuration Template
# Copy this file to .env.keycloak and fill in your actual values

# =============================================================================
# KEYCLOAK SERVER CONFIGURATION
# =============================================================================

# Your Keycloak server base URL (without /auth if using newer versions)
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth

# The realm where your clients are configured
KEYCLOAK_REALM=master

# =============================================================================
# INITIAL CLIENT CONFIGURATION
# =============================================================================
# This is the client that will get the first token (subject token)

# Client ID for initial authentication
INITIAL_CLIENT_ID=initial-client

# Client secret (leave empty if public client)
INITIAL_CLIENT_SECRET=your-initial-client-secret

# User credentials for password grant
KEYCLOAK_USERNAME=your-username
KEYCLOAK_PASSWORD=your-password

# =============================================================================
# REQUESTER CLIENT CONFIGURATION  
# =============================================================================
# This is the client that performs the token exchange

# Client ID that will request token exchange
REQUESTER_CLIENT_ID=requester-client

# Client secret for requester client (required for token exchange)
REQUESTER_CLIENT_SECRET=your-requester-client-secret

# =============================================================================
# TARGET CLIENT CONFIGURATION
# =============================================================================
# This is the intended audience for the exchanged token

# Target client ID (will be used as audience parameter)
TARGET_CLIENT_ID=target-client

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

# Enable verbose logging (true/false)
VERBOSE=true

# Skip initial authentication and use provided subject token (true/false)
SKIP_INITIAL_AUTH=false

# Pre-existing subject token (if SKIP_INITIAL_AUTH=true)
SUBJECT_TOKEN=

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT SCENARIOS
# =============================================================================

# Scenario 1: Testing with public initial client
# INITIAL_CLIENT_SECRET=
# REQUESTER_CLIENT_SECRET=your-confidential-client-secret

# Scenario 2: Testing with pre-existing token
# SKIP_INITIAL_AUTH=true
# SUBJECT_TOKEN=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...

# Scenario 3: Testing multiple audiences
# You can modify the script to test multiple target clients

# =============================================================================
# KEYCLOAK CLIENT SETUP REQUIREMENTS
# =============================================================================

# For this test to work, you need to configure the following in Keycloak:

# 1. INITIAL CLIENT:
#    - Can be public or confidential
#    - Must support "Direct Access Grants" (Resource Owner Password Credentials)
#    - User must exist and have appropriate permissions

# 2. REQUESTER CLIENT:
#    - MUST be confidential (has client secret)
#    - Must have "Standard Token Exchange" enabled in client settings
#    - Must have appropriate service account roles if using client credentials

# 3. TARGET CLIENT:
#    - Can be any client that should receive the exchanged token
#    - Will be used as the audience parameter in token exchange

# 4. REALM SETTINGS:
#    - Token exchange feature must be enabled (usually enabled by default)
#    - Appropriate client scopes and roles must be configured

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common issues and solutions:

# 1. "invalid_grant" error:
#    - Check that REQUESTER_CLIENT has "Standard Token Exchange" enabled
#    - Verify client credentials are correct
#    - Ensure subject token is valid and not expired

# 2. "unauthorized_client" error:
#    - REQUESTER_CLIENT must be confidential (not public)
#    - Check client authentication method

# 3. "invalid_audience" error:
#    - TARGET_CLIENT_ID must exist in the same realm
#    - Check audience parameter spelling

# 4. "access_denied" error:
#    - Check user permissions and roles
#    - Verify client scopes and role mappings
