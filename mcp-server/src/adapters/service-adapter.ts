/**
 * 🔧 Service Adapter - 自动化服务工具适配器
 */

import { z } from 'zod';
import { getEnvironmentConfig } from '../utils/env.js';

/**
 * 🔧 Zod Schemas for MCP Tools
 */

// OMS getOrderDetail schema
const GetOrderDetailArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
  orderNO: z.string().optional().describe('Order number (optional)'),
  ikeaOrderNO: z.string().optional().describe('IKEA order number (optional)'),
});

// OMS queryOrderLists schema
const QueryOrderListsArgsSchema = z.object({
  page: z.number().min(1).default(1).describe('Page number (starting from 1)'),
  pageSize: z.number().min(1).max(100).default(10).describe('Number of items per page (1-100)'),
  orderStatus: z.string().optional().describe('Filter by order status'),
  storeCode: z.string().optional().describe('Filter by store code'),
  startDate: z.string().optional().describe('Start date filter (YYYY-MM-DD)'),
  endDate: z.string().optional().describe('End date filter (YYYY-MM-DD)'),
});

// OMS logistics/assembling/retrieval schemas (all need vid)
const VidArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
});

// Global getCurrentUser schema
const GetCurrentUserArgsSchema = z.object({});

// Test schemas
const TestEchoArgsSchema = z.object({
  message: z.string().optional().describe('Message to echo back'),
  data: z.any().optional().describe('Data to echo back'),
});

const TestPingArgsSchema = z.object({});

/**
 * 🔧 安全的 JSON 序列化函数
 */
function safeStringify(obj: any, indent: number = 0): string {
  const seen = new WeakSet();

  const replacer = (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }

    if (value instanceof Error) {
      return {
        name: value.name,
        message: value.message,
        stack: value.stack,
      };
    }

    if (typeof value === 'function') {
      return '[Function]';
    }

    if (value === undefined) {
      return '[Undefined]';
    }

    return value;
  };

  try {
    return JSON.stringify(obj, replacer, indent);
  } catch (error) {
    return `[Object: ${typeof obj}]`;
  }
}

/**
 * 🔧 服务模块实现
 */
export const SERVICE_MODULES = {
  oms: {
    queryOrderLists: async (data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: '/orders/search',
          method: 'POST',
          data: {
            page: data.page || 1,
            pageSize: data.pageSize || 10,
            orderStatus: data.orderStatus,
            storeCode: data.storeCode,
            startDate: data.startDate,
            endDate: data.endDate,
          },
          useKong: true,
          cookies,
        })
      );
    },

    getOrderDetail: async (data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      const params = typeof data === 'string' ? { vid: data } : data;

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: '/order/detail',
          method: 'POST',
          data: {
            vid: params.vid,
            orderNO: params.orderNO,
            ikeaOrderNO: params.ikeaOrderNO,
          },
          useKong: true,
          cookies,
        })
      );
    },

    getLogisticsInfo: async (data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: `/logistics/${data.vid}`,
          method: 'GET',
          useKong: true,
          cookies,
        })
      );
    },

    getAssemblingInfo: async (data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: `/assembling/${data.vid}`,
          method: 'GET',
          useKong: true,
          cookies,
        })
      );
    },

    searchPermissionStore: async (_data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: '/user/searchPermissionStore',
          method: 'GET',
          useKong: true,
          cookies,
        })
      );
    },

    getRetrievalData: async (data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: `/order/${data.vid}/retrieval`,
          method: 'GET',
          useKong: true,
          cookies,
        })
      );
    },
  },

  global: {
    getCurrentUser: async (_data: any) => {
      const env = getEnvironmentConfig();
      const cookies = env.AUTH_COOKIES;

      if (!cookies) {
        throw new Error('Authentication cookies are required');
      }

      return await import('../utils/http.js').then(({ default: http }) =>
        http({
          url: '/user/current',
          method: 'GET',
          useKong: true,
          cookies,
        })
      );
    },
  },

  test: {
    ping: async (data: any) => {
      return {
        success: true,
        message: 'pong',
        timestamp: new Date().toISOString(),
        data: data || null,
      };
    },

    echo: async (data: any) => {
      return {
        success: true,
        message: 'echo response',
        timestamp: new Date().toISOString(),
        echo: data,
      };
    },
  },
};

/**
 * 🔧 工具配置
 */
export const SERVICE_TOOL_CONFIGS = {
  oms: [
    {
      name: 'queryOrderLists',
      description: 'Query order lists from OMS system',
      zodSchema: QueryOrderListsArgsSchema,
    },
    {
      name: 'getOrderDetail',
      description: 'Get detailed information for a specific order',
      zodSchema: GetOrderDetailArgsSchema,
    },
    {
      name: 'getLogisticsInfo',
      description: 'Get logistics information for an order',
      zodSchema: VidArgsSchema,
    },
    {
      name: 'getAssemblingInfo',
      description: 'Get assembling information for an order',
      zodSchema: VidArgsSchema,
    },
    {
      name: 'searchPermissionStore',
      description: 'Search permission stores for current user',
      zodSchema: GetCurrentUserArgsSchema,
    },
    {
      name: 'getRetrievalData',
      description: 'Get retrieval data for an order',
      zodSchema: VidArgsSchema,
    },
  ],

  global: [
    {
      name: 'getCurrentUser',
      description: 'Get current user information',
      zodSchema: GetCurrentUserArgsSchema,
    },
  ],

  test: [
    {
      name: 'ping',
      description: 'Quick ping test - returns immediately',
      zodSchema: TestPingArgsSchema,
    },
    {
      name: 'echo',
      description: 'Echo test - returns input data',
      zodSchema: TestEchoArgsSchema,
    },
  ],
};

/**
 * 🔧 创建服务工具处理器
 */
function createServiceToolHandler(moduleName: string, functionName: string, zodSchema?: any) {
  return async (args: any) => {
    const requestId = Math.random().toString(36).substring(2, 15);
    const timestamp = new Date().toISOString();

    console.error(`🔧 [${timestamp}] ==================== SERVICE TOOL [${requestId}] ====================`);
    console.error(`🔧 Module: ${moduleName}`);
    console.error(`🔧 Function: ${functionName}`);
    console.error(`🔧 Request ID: ${requestId}`);
    console.error(`🔧 Arguments:`, safeStringify(args, 2));

    try {
      // Zod 参数验证
      let validatedArgs = args;
      if (zodSchema) {
        try {
          console.error(`🔍 Validating arguments with Zod schema...`);
          validatedArgs = zodSchema.parse(args);
          console.error(`✅ Arguments validation successful`);
        } catch (zodError: any) {
          console.error(`❌ Arguments validation failed:`, zodError.errors);
          return {
            success: false,
            error: 'Invalid arguments provided',
            details: {
              validationErrors: zodError.errors,
              receivedArgs: args,
            },
            meta: {
              module: moduleName,
              function: functionName,
              requestId,
            },
          };
        }
      }

      // 获取服务函数
      const serviceModule = (SERVICE_MODULES as any)[moduleName];
      if (!serviceModule) {
        throw new Error(`Service module '${moduleName}' not found`);
      }

      const serviceFunction = serviceModule[functionName];
      if (!serviceFunction) {
        throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);
      }

      // 传递参数 - 使用验证后的参数
      let functionArgs;
      if (validatedArgs.data !== undefined) {
        functionArgs = validatedArgs.data;
      } else {
        // 创建一个副本，移除 cookies 字段（因为它是 MCP 层面的参数）
        functionArgs = { ...validatedArgs };
        delete functionArgs.cookies;
      }

      console.error(`🚀 Calling service function with args:`, safeStringify(functionArgs, 2));

      // 调用服务函数
      const result = await serviceFunction(functionArgs);

      console.error(`✅ [${requestId}] SERVICE SUCCESS`);
      console.error(`📊 Result type: ${typeof result}`);
      console.error(`📊 Result keys: ${result && typeof result === 'object' ? Object.keys(result) : 'N/A'}`);

      return {
        success: true,
        data: result,
        meta: {
          module: moduleName,
          function: functionName,
          requestId,
          timestamp,
        },
      };

    } catch (error: any) {
      console.error(`❌ [${requestId}] SERVICE ERROR: ${error.message}`);
      console.error(`📊 Error stack:`, error.stack);

      return {
        success: false,
        error: error.message,
        details: {
          stack: error.stack,
          name: error.name,
        },
        meta: {
          module: moduleName,
          function: functionName,
          requestId,
          timestamp,
        },
      };
    }
  };
}

/**
 * 🚀 自动注册所有服务工具到 MCP 服务器
 */
export function registerAllServiceTools(server: any) {
  console.error('🔧 [ServiceAdapter] Auto-registering service tools...');

  const env = getEnvironmentConfig();
  console.error('🌍 [ServiceAdapter] Environment Configuration:');
  console.error(`   NODE_ENV: ${env.NODE_ENV}`);
  console.error(`   API Host: ${env.VITE_API_HOST}`);
  console.error(`   Kong Host: ${env.VITE_API_HOST_KONG}`);
  console.error(`   Auth Cookies: ${env.AUTH_COOKIES ? '✅ Configured' : '❌ Missing'}`);
  console.error(`   Debug Mode: ${env.DEBUG_SERVICE_ADAPTER ? '✅ Enabled' : '❌ Disabled'}`);

  let totalTools = 0;

  // 遍历所有服务模块
  Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
    console.error(`🔧 [ServiceAdapter] Registering tools for module: ${moduleName}`);

    functions.forEach(func => {
      const toolName = `${moduleName}_${func.name}`;

      try {
        // 创建工具处理器
        const handler = createServiceToolHandler(moduleName, func.name, (func as any).zodSchema);

        // 注册到 MCP 服务器 - 传递 Zod schema 而不是 JSON schema
        const zodSchema = (func as any).zodSchema;
        if (zodSchema) {
          console.error(`🔧 [ServiceAdapter] Registering tool ${toolName} with Zod schema`);
          server.tool(toolName, func.description, zodSchema, async (args: any) => {
            const result = await handler(args);

            // 返回 MCP 格式的响应，使用安全的序列化方式
            let resultText;
            try {
              resultText = safeStringify(result, 2);
            } catch (jsonError) {
              console.error(`❌ [ServiceAdapter] Failed to serialize result:`, jsonError);
              resultText = safeStringify({
                success: result?.success || false,
                error: result?.error || 'Serialization failed',
                meta: result?.meta || {},
              }, 2);
            }

            return {
              content: [{
                type: 'text',
                text: resultText
              }]
            };
          });
        } else {
          console.error(`🔧 [ServiceAdapter] Registering tool ${toolName} without schema`);
          server.tool(toolName, func.description, async (args: any) => {
            const result = await handler(args);

            // 返回 MCP 格式的响应，使用安全的序列化方式
            let resultText;
            try {
              resultText = safeStringify(result, 2);
            } catch (jsonError) {
              console.error(`❌ [ServiceAdapter] Failed to serialize result:`, jsonError);
              resultText = safeStringify({
                success: result?.success || false,
                error: result?.error || 'Serialization failed',
                meta: result?.meta || {},
              }, 2);
            }

            return {
              content: [{
                type: 'text',
                text: resultText
              }]
            };
          });
        }

        totalTools++;
        console.error(`🔧 [ServiceAdapter] ✅ Registered tool: ${toolName}`);

      } catch (error: any) {
        console.error(`🔧 [ServiceAdapter] ❌ Failed to register tool: ${toolName}`);
        console.error(`   Error: ${error.message}`);
      }
    });
  });

  console.error(`🎉 [ServiceAdapter] Successfully registered ${totalTools} service tools`);
  return totalTools;
}