# Environment Configuration for Orders Portal MCP Server

# Node Environment
NODE_ENV=development

# Dev API Endpoints
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web

VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

VITE_MASTER_DATA_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/master-data

VITE_APP_ID=orders-portal

VITE_STORE_CODES_REQUIRING_REFUND_REVIEW=819,957

VITE_ACTIVITY_PARTICIPATE_LINK=/app/promotion-sit/tool/activityparticipate?orderId=<%=orderId%>

VITE_SURVEY_LINK=https://res.app.ikea.cn/modules/ikeacn-portal/universal/survey-coupons.html?code=yp4BmrnQMd

VITE_IMG_FALLBACK_URL=data:image/png;base64,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

# Authentication
# Set your authentication cookies here for API access
# Example: AUTH_COOKIES=session_id=abc123; auth_token=xyz789
AUTH_COOKIES=test_orders-portal=NDIzMTAwZTgtMWMwZC00YTAwLWExxxxxxxxxxxx

# Set the X-Custom-Referrer header to mimic web app requests
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# ================================
# OAuth2 Configuration
# ================================
# Enable OAuth2 authentication with Keycloak
OAUTH2_ENABLED=true

# Keycloak server configuration
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master

# OAuth2 client configuration
# These values must match your Keycloak client configuration
OAUTH2_CLIENT_ID=mcp-mpc-odi
OAUTH2_CLIENT_SECRET=JHULZUN7XUxaFxwSrq2BSsta0nbpEmt1

# OAuth2 redirect URI (must be registered in Keycloak)
OAUTH2_REDIRECT_URI=http://localhost:3000/auth/callback

# OAuth2 scopes (comma-separated)
# Common scopes: openid, profile, email, offline_access
OAUTH2_SCOPES=openid,profile,email

# MCP server configuration
MCP_SERVER_BASE_URL=http://localhost:3000
MCP_SERVER_PORT=3000

# ================================
# Keycloak Client Setup Instructions
# ================================
# 1. Login to Keycloak Admin Console: https://keycloak.ingka-dt.cn/auth/admin/
# 2. Select your realm (e.g., 'master')
# 3. Go to Clients → Create Client
# 4. Set Client ID to: mcp-server
# 5. Set Client Type to: OpenID Connect
# 6. Enable Client Authentication: ON
# 7. Set Valid Redirect URIs to: http://localhost:3000/*
# 8. Set Web Origins to: http://localhost:3000
# 9. In Credentials tab, copy the Client Secret
# 10. In Client Scopes tab, ensure required scopes are assigned

# ================================
# OAuth2 Endpoints (Auto-generated)
# ================================
# These are automatically constructed from the configuration above:
# Authorization: https://keycloak.ingka-dt.cn/realms/master/protocol/openid-connect/auth
# Token: https://keycloak.ingka-dt.cn/realms/master/protocol/openid-connect/token
# UserInfo: https://keycloak.ingka-dt.cn/realms/master/protocol/openid-connect/userinfo
# JWKS: https://keycloak.ingka-dt.cn/realms/master/protocol/openid-connect/certs
# Revocation: https://keycloak.ingka-dt.cn/realms/master/protocol/openid-connect/revoke

# ================================
# MCP Server Endpoints (When OAuth2 is enabled)
# ================================
# Health Check: http://localhost:3000/health
# OAuth2 Metadata: http://localhost:3000/.well-known/oauth-authorization-server
# Authorization: http://localhost:3000/authorize
# Token: http://localhost:3000/token
# User Info: http://localhost:3000/userinfo
# Token Introspection: http://localhost:3000/introspect
# MCP Tools: http://localhost:3000/tools
# Execute Tool: http://localhost:3000/tools/{toolName}

# ================================
# Testing OAuth2 Flow
# ================================
# 1. Start the server: npm run dev
# 2. Visit: http://localhost:3000/health (should show oauth2_enabled: true)
# 3. Test authorization flow:
#    GET http://localhost:3000/authorize?response_type=code&client_id=mcp-server&redirect_uri=http://localhost:3000/auth/callback&scope=openid profile email&state=test123&code_challenge=CHALLENGE&code_challenge_method=S256
# 4. After successful login, exchange code for token:
#    POST http://localhost:3000/token
#    Content-Type: application/x-www-form-urlencoded
#    grant_type=authorization_code&code=CODE&redirect_uri=http://localhost:3000/auth/callback&client_id=mcp-server&client_secret=SECRET&code_verifier=VERIFIER
# 5. Use access token to call protected endpoints:
#    GET http://localhost:3000/userinfo
#    Authorization: Bearer ACCESS_TOKEN